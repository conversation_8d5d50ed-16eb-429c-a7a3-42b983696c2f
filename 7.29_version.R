library(readxl)
library(dplyr)
library(tidyr)
library(stringr)
library(mlogit)


raw <- read_excel("data.xlsx", sheet = 1)
title <- raw[1:2,]
raw <- raw[-c(1,2),]


raw <- raw %>%
  rename(
    Q7 = starts_with("Q7. Cheap talk")
  )%>%
  
  mutate(Q7_trim = str_trim(Q7)) %>%
  # 然后根据修剪后的文本来生成 treatment
  mutate(treatment = case_when(
    is.na(Q7_trim)                      ~ NA_integer_,       # 原本就没回答的，保留 NA
    Q7_trim == "-"                      ~ 0L,                # "-" 当对照组
    str_starts(Q7_trim, "Yes")          ~ 1L,                # 以 "Yes" 开头的当处理组
    TRUE                                ~ 0L          
    )
  )

table(raw$treatment, useNA="ifany")

names(raw)[21:28] <- paste0("Q", 1:8, "_choice")


design <- read.csv("design_matrix.csv", stringsAsFactors = FALSE)

# 检查 design_matrix.csv 结构
print("Design matrix structure:")
print(str(design))
print("Design matrix summary:")
print(summary(design))

# 1. 挑出 respondent_id、treatment 以及刚刚重命名的 Q*_choice
dce_raw <- raw %>%
  select(respondent_id = UserNo,
         treatment,
         Q1_choice:Q8_choice)

# 2. 宽→长
dce_long <- dce_raw %>%
  pivot_longer(
    cols      = Q1_choice:Q8_choice,
    names_to  = "qn",
    values_to = "chosen_Option"
  ) %>%
  mutate(
    task_id = as.integer(str_extract(qn, "\\d+"))
  ) %>%
  select(respondent_id, task_id, chosen_Option)


# 0. 规范 dce_long
dce_long <- dce_long %>%
  mutate(
    chosen_Option = str_extract(chosen_Option, "[ABC]")  
  ) %>%
  filter(!is.na(chosen_Option)) %>%
  select(respondent_id, task_id, chosen_Option)

# 检查 dce_long 结构
print("DCE long structure:")
print(str(dce_long))
print("Choice distribution:")
print(table(dce_long$chosen_Option, useNA="ifany"))

task_counts <- dce_long %>%
  distinct(respondent_id, task_id) %>%  # 每人每题只算一次
  count(respondent_id, name = "n_answered")


head(task_counts)

# 整体分布
summary(task_counts$n_answered)
table(task_counts$n_answered)




# 1. 提取 respondent × task 唯一组合
resp_tasks <- dce_long %>%
  distinct(respondent_id, task_id)

# 2. 笛卡尔积：为每个 respondent × task 分配三个 Option（A/B/C）
options <- design %>% distinct(Option)
resp_task_options <- crossing(resp_tasks, options)

# 3. 合并 design 中的属性值
df_model <- resp_task_options %>%
  left_join(design, by = c("task_id" = "Task", "Option" = "Option"))

# 检查 df_model 结构
print("DF model structure:")
print(str(df_model))

# 4. 用 choice_flag 打标 choice
dce_flags <- dce_long %>%
  transmute(respondent_id, task_id, Option = chosen_Option, choice_flag = 1L)

df_model <- df_model %>%
  left_join(dce_flags,
            by = c("respondent_id","task_id","Option")) %>%
  # 没匹配到的就是 0
  mutate(choice = replace_na(choice_flag, 0L)) %>%
  select(-choice_flag)

# 检查 choice 分布
print("Choice distribution in df_model:")
print(table(df_model$choice, useNA="ifany"))

df_model <- df_model %>%
  mutate(
    # 为 opt‑out 生成常数
    ASC_C           = as.integer(Option == "C"),
    # 把 C 的属性设成 NA，避免它在虚拟变量中被当作一个水平
    MeatType        = ifelse(Option == "C", NA, MeatType),
    LivestockEffect = ifelse(Option == "C", NA, LivestockEffect),
    AntibioticUse   = ifelse(Option == "C", NA, AntibioticUse),
    # 属性虚拟变量（以一个水平作为基线）
    meat_Cultured   = as.integer(MeatType == "Cultured"),
    meat_PlantBased = as.integer(MeatType == "Plant based"),
    impact_Medium   = as.integer(LivestockEffect == "Medium"),
    impact_High     = as.integer(LivestockEffect == "High"),
    ab_medium = as.integer(AntibioticUse == "Medium"),
    ab_high   = as.integer(AntibioticUse == "High")
  ) %>%
  # 把所有 NA 的虚拟变量设为 0
  replace_na(list(
    meat_Cultured   = 0,
    meat_PlantBased = 0,
    impact_Medium   = 0,
    impact_High     = 0,
    ab_medium = 0,
    ab_high   = 0
  ))

# 检查虚拟变量分布
print("Virtual variables distribution:")
print("meat_Cultured:")
print(table(df_model$meat_Cultured, useNA="ifany"))
print("meat_PlantBased:")
print(table(df_model$meat_PlantBased, useNA="ifany"))
print("impact_Medium:")
print(table(df_model$impact_Medium, useNA="ifany"))
print("impact_High:")
print(table(df_model$impact_High, useNA="ifany"))
print("ab_medium:")
print(table(df_model$ab_medium, useNA="ifany"))
print("ab_high:")
print(table(df_model$ab_high, useNA="ifany"))
print("Price summary:")
print(summary(df_model$Price))

df_model_clean <- df_model %>%
  distinct(respondent_id, task_id, Option, .keep_all = TRUE) %>%
  mutate(
    # 把 respondent_id 和 task_id 结合成一个新 ID
    chid = paste(respondent_id, task_id, sep = "_"),
    choice = choice == 1
  )

# 检查 df_model_clean 结构
print("DF model clean structure:")
print(str(df_model_clean))

# 再用新的来构造 mlogit 数据
mlogit_df <- mlogit.data(
  df_model_clean,
  choice   = "choice",
  shape    = "long",
  alt.var  = "Option",
  chid.var = "chid",
  id.var   = "respondent_id"
)

# 验证结构
str(mlogit_df)
head(mlogit_df)

# 检查 mlogit_df 中的变量分布
print("MLogit data summary:")
print(summary(mlogit_df))

# 检查是否有完全共线的变量
print("Checking for perfect collinearity...")
mlogit_vars <- mlogit_df[, c("meat_Cultured", "meat_PlantBased", "impact_Medium", "impact_High", "ab_medium", "ab_high", "Price")]
print("Correlation matrix:")
print(cor(mlogit_vars, use="complete.obs"))

# 检查每个虚拟变量的分布
print("Virtual variables in mlogit data:")
for(var in c("meat_Cultured", "meat_PlantBased", "impact_Medium", "impact_High", "ab_medium", "ab_high")) {
  print(paste(var, "distribution:"))
  print(table(mlogit_df[[var]], useNA="ifany"))
}

library(mlogit)

# 尝试简化模型，先只包含部分变量
clogit_model <- mlogit(
  choice ~ meat_Cultured + meat_PlantBased + Price | 1,
  data = mlogit_df
)

summary(clogit_model)

# 如果简化模型成功，再尝试完整模型
clogit_model_full <- mlogit(
  choice ~ meat_Cultured + meat_PlantBased +
    impact_Medium + impact_High +
    ab_medium + ab_high +
    Price | 1,
  data = mlogit_df
)

summary(clogit_model_full)

library(gmnl)

# 1. 定义随机 WTP 参数（这里所有属性都允许个体异质）
rpar_wtp <- c(
  meat_Cultured   = "n",
  meat_PlantBased = "n",
  impact_Medium   = "n",
  impact_High     = "n",
  ab_medium       = "n",
  ab_high         = "n"
)

# 2. 调用 gmnl，指定 space="wtp"
wtp_space_model <- gmnl(
  choice ~ meat_Cultured + meat_PlantBased +
    impact_Medium + impact_High +
    ab_medium + ab_high |
    1,              # alternative‐specific constants
  data  = mlogit_df,  
  model = "mixl",    
  ranp  = rpar_wtp,   
  R     = 500,       
  panel = TRUE,       
  space = "wtp"       
)

# 3. 查看结果
summary(wtp_space_model)





